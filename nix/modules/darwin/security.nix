# Darwin security configuration
{username, pkgs, lib, ...}: {
  # PAM configuration
  security.pam.services.sudo_local = {
    touchIdAuth = true;
    watchIdAuth = true;
  };

  # System security defaults
  system.defaults = {
    screensaver = {
      askForPassword = true;
      askForPasswordDelay = 0;
    };

    loginwindow = {
      GuestEnabled = false;
      # DisableConsoleAccess = true;  # Conflicts with host-specific setting
      SHOWFULLNAME = true;
      # LoginwindowText = "Authorized users only";  # Conflicts with host-specific setting
    };

    # NSGlobalDomain screensaver settings are handled by screensaver section

    finder = {
      FXEnableExtensionChangeWarning = lib.mkDefault true;
      AppleShowAllExtensions = lib.mkDefault true;
      # WarnOnEmptyTrash = true;  # This option may not be available
    };

    dock = {
      show-recents = false;
    };
  };
}