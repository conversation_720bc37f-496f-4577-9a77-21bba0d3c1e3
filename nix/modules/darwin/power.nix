# Darwin power management configuration
# macOS-specific power settings and optimizations
{username, pkgs, ...}: {
  # Power management settings
  # Note: Most power settings in macOS are managed through System Preferences
  # or pmset command line tool. nix-<PERSON><PERSON><PERSON> has limited power management options.

  # Power-related launchd services
  launchd.daemons = {
    # Battery health monitoring
    battery-monitor = {
      serviceConfig = {
        Label = "local.battery.monitor";
        ProgramArguments = [
          "/bin/bash"
          "-c"
          ''
            # Check battery health and log warnings
            battery_info=$(system_profiler SPPowerDataType 2>/dev/null | grep -A 5 "Condition")
            if echo "$battery_info" | grep -q "Replace"; then
              echo "$(date): WARNING - Battery needs replacement" >> /var/log/battery-monitor.log
            fi
            
            # Log current battery cycle count
            cycle_count=$(system_profiler SPPowerDataType 2>/dev/null | grep "Cycle Count" | awk '{print $3}')
            if [ -n "$cycle_count" ] && [ "$cycle_count" -gt 1000 ]; then
              echo "$(date): INFO - Battery cycle count: $cycle_count (high)" >> /var/log/battery-monitor.log
            fi
          ''
        ];
        StartInterval = 3600; # Check every hour
        StandardOutPath = "/var/log/battery-monitor.log";
        StandardErrorPath = "/var/log/battery-monitor.log";
      };
    };
  };

  # Power management user agents (per-user services)
  launchd.agents = {
    # Caffeine-like service to prevent sleep during specific conditions
    prevent-sleep-on-activity = {
      serviceConfig = {
        Label = "local.power.prevent-sleep";
        ProgramArguments = [
          "/bin/bash"
          "-c"
          ''
            # Prevent sleep if certain conditions are met
            # Check for active downloads, builds, or other long-running processes
            
            # Check for active downloads (example: aria2, wget, curl)
            if pgrep -f "(aria2|wget|curl.*-O)" >/dev/null; then
              caffeinate -d -t 3600 &  # Prevent display sleep for 1 hour
              echo "$(date): Preventing sleep due to active downloads" >> ~/Library/Logs/prevent-sleep.log
            fi
            
            # Check for active builds (example: make, cargo, npm)
            if pgrep -f "(make|cargo build|npm.*build|nix.*build)" >/dev/null; then
              caffeinate -i -t 7200 &  # Prevent idle sleep for 2 hours
              echo "$(date): Preventing sleep due to active builds" >> ~/Library/Logs/prevent-sleep.log
            fi
          ''
        ];
        StartInterval = 300; # Check every 5 minutes
        StandardOutPath = "/Users/<USER>/Library/Logs/prevent-sleep.log";
        StandardErrorPath = "/Users/<USER>/Library/Logs/prevent-sleep.log";
      };
    };
  };
}
