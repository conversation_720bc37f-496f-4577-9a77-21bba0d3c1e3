# Darwin networking configuration
# macOS-specific network settings and optimizations
{username, pkgs, ...}: {
  # Basic networking configuration
  # Note: macOS networking is mostly managed through System Preferences
  # These are the available nix-darwin networking options

  # macOS-specific network optimizations
  # Most network settings are managed through System Preferences

  # Custom network-related launchd services
  launchd.daemons = {
    # Network connectivity check service
    network-check = {
      serviceConfig = {
        Label = "local.network.check";
        ProgramArguments = [
          "/bin/bash"
          "-c"
          ''
            # Simple network connectivity check
            if ping -c 1 1.1.1.1 >/dev/null 2>&1; then
              echo "$(date): Network connectivity OK" >> /var/log/network-check.log
            else
              echo "$(date): Network connectivity FAILED" >> /var/log/network-check.log
            fi
          ''
        ];
        StartInterval = 300; # Check every 5 minutes
        StandardOutPath = "/var/log/network-check.log";
        StandardErrorPath = "/var/log/network-check.log";
      };
    };

    # DNS cache flush service (useful for development)
    dns-cache-flush = {
      serviceConfig = {
        Label = "local.dns.cache.flush";
        ProgramArguments = [
          "/usr/bin/dscacheutil"
          "-flushcache"
        ];
        # This service can be triggered manually: sudo launchctl start local.dns.cache.flush
        RunAtLoad = false;
      };
    };
  };

  # Network monitoring and diagnostics
  # Most network tools are already in home-manager configuration
}
