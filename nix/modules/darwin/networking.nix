# Darwin networking configuration
# macOS-specific network settings and optimizations
{username, ...}: {
  # Basic networking configuration
  networking = {
    # DNS configuration - use Cloudflare and Google DNS for better performance
    dns = [
      "*******"
      "*******"
      "*******"
      "*******"
    ];

    # Search domains (can be customized per environment)
    search = [
      "local"
    ];

    # Enable IPv6
    enableIPv6 = true;

    # Network interface configuration
    interfaces = {
      # Ethernet interface optimization
      en0 = {
        # Enable jumbo frames if supported by network infrastructure
        # mtu = 9000;  # Uncomment if your network supports jumbo frames
      };
    };
  };

  # macOS-specific network optimizations
  # These are applied via system defaults since macOS doesn't use sysctl for networking like Linux
  system.defaults.NSGlobalDomain = {
    # Network-related global domain settings
    # Enable network time synchronization
    "com.apple.networkTime" = true;
  };

  # Custom network-related launchd services
  launchd.daemons = {
    # Network connectivity check service
    network-check = {
      serviceConfig = {
        Label = "local.network.check";
        ProgramArguments = [
          "/bin/bash"
          "-c"
          ''
            # Simple network connectivity check
            if ping -c 1 ******* >/dev/null 2>&1; then
              echo "$(date): Network connectivity OK" >> /var/log/network-check.log
            else
              echo "$(date): Network connectivity FAILED" >> /var/log/network-check.log
            fi
          ''
        ];
        StartInterval = 300; # Check every 5 minutes
        StandardOutPath = "/var/log/network-check.log";
        StandardErrorPath = "/var/log/network-check.log";
      };
    };

    # DNS cache flush service (useful for development)
    dns-cache-flush = {
      serviceConfig = {
        Label = "local.dns.cache.flush";
        ProgramArguments = [
          "/usr/bin/dscacheutil"
          "-flushcache"
        ];
        # This service can be triggered manually: sudo launchctl start local.dns.cache.flush
        RunAtLoad = false;
      };
    };
  };

  # Network security settings
  system.defaults.NSGlobalDomain = {
    # Disable automatic network discovery
    "com.apple.NetworkBrowser.BrowseAllInterfaces" = false;
    
    # Disable Bonjour multicast advertisements
    "com.apple.mDNSResponder.NoMulticastAdvertisements" = true;
  };

  # Firewall configuration (basic)
  # Note: macOS firewall is managed through System Preferences or pfctl
  # For advanced firewall rules, consider using pfctl configuration files
  
  # Network monitoring and diagnostics
  environment.systemPackages = with pkgs; [
    # Network diagnostic tools (if not already in home-manager)
    # These are useful for system-level network troubleshooting
  ];
}
