# Darwin host configuration
# This file contains host-specific configurations that should not be shared between different machines
{username, pkgs, ...}: {
  # System state version - this is host-specific and should not be changed after initial installation
  system.stateVersion = 6;

  # Set primary user for this host
  system.primaryUser = username;

  # Host-specific user configuration
  users.users = {
    # Main user configuration for this specific machine
    ${username} = {
      home = "/Users/<USER>";
      description = username;
      shell = "/etc/profiles/per-user/${username}/bin/zsh";
    };

    # Ops user (from ansible disk.yml)
    # Note: Creating users on macOS requires different approach
    ops = {
      home = "/Users/<USER>";
      description = "Operations user";
      shell = "/bin/bash";
    };
  };

  # Host-specific Nix settings
  nix.settings.trusted-users = [username];

  # Ensure shells are available in /etc/shells for chsh
  environment.shells = [
    "/etc/profiles/per-user/${username}/bin/zsh"
    "/etc/profiles/per-user/${username}/bin/bash"
    "/etc/profiles/per-user/${username}/bin/fish"
    "/run/current-system/sw/bin/zsh"
    "/run/current-system/sw/bin/bash"
    "/run/current-system/sw/bin/fish"
    "/bin/zsh"
    "/bin/bash"
    "/bin/fish"
    "/usr/bin/zsh"
    "/usr/bin/bash"
    "/usr/bin/fish"
  ];

  # Host-specific network configuration
  networking.hostName = "darwin-${username}";
  networking.computerName = "${username}'s MacBook Pro";
  networking.localHostName = "darwin-${username}";

  # Host-specific system preferences
  system.defaults = {
    # Global preferences
    NSGlobalDomain = {
      # Time configuration
      AppleICUForce24HourTime = true;
      AppleInterfaceStyle = null; # Dark or null for light mode

      # Keyboard settings
      KeyRepeat = 2; # Set to fastest
      InitialKeyRepeat = 15;
      AppleKeyboardUIMode = 3;

      # Finder settings
      AppleShowAllExtensions = true;
      AppleShowAllFiles = true;
      NSNavPanelExpandedStateForSaveMode = true;
      NSNavPanelExpandedStateForSaveMode2 = true;
      PMPrintingExpandedStateForPrint = true;
      PMPrintingExpandedStateForPrint2 = true;

      # Trackpad settings
      "com.apple.trackpad.scaling" = 1.5;
      "com.apple.mouse.tapBehavior" = 1;
      "com.apple.trackpad.enableSecondaryClick" = true;

      # Interface settings
      ApplePressAndHoldEnabled = false;
      AppleScrollerPagingBehavior = false;
      AppleWindowTabbingMode = "manual";
      NSAutomaticWindowAnimationsEnabled = false;
      NSUseAnimatedFocusRing = false;

      # Automatic spelling correction
      NSAutomaticSpellingCorrectionEnabled = false;
      NSAutomaticCapitalizationEnabled = false;
      NSAutomaticDashSubstitutionEnabled = false;
      NSAutomaticPeriodSubstitutionEnabled = false;
      NSAutomaticQuoteSubstitutionEnabled = false;

      # Screenshots - these options may not be available in nix-darwin
      # "com.apple.screencapture.location" = "~/Desktop/Screenshots";
      # "com.apple.screencapture.disable-shadow" = true;
    };

    # Dock settings
    dock = {
      autohide = true;
      autohide-delay = 0.0;
      autohide-time-modifier = 0.0;
      orientation = "left";
      tilesize = 48;
      largesize = 64;
      magnification = false;
      show-recents = false;
      persistent-apps = [];
      static-only = false;
      launchanim = false;
    };

    # Finder settings
    finder = {
      AppleShowAllFiles = true;
      ShowPathbar = true;
      ShowStatusBar = true;
      FXDefaultSearchScope = "SCcf";
      FXPreferredViewStyle = "clmv"; # Column view
      CreateDesktop = false;
      FXEnableExtensionChangeWarning = false;
      _FXShowPosixPathInTitle = true;
      QuitMenuItem = true;
    };

    # Trackpad settings
    trackpad = {
      Clicking = true;
      TrackpadThreeFingerDrag = true;
    };

    # Screensaver settings
    screensaver = {
      askForPassword = true;
      askForPasswordDelay = 0;
    };

    # Window manager settings
    WindowManager = {
      AutoHide = false;
      EnableStandardClickToShowDesktop = true;
      EnableTiledWindowMargins = true;
      EnableTilingByEdgeDrag = true;
      EnableTilingOptionAccelerator = true;
      EnableTopTilingByEdgeDrag = true;
      GloballyEnabled = false;
      HideDesktop = false;
      StandardHideDesktopIcons = false;
      StandardHideWidgets = false;
      StageManagerHideWidgets = false;
    };

    # Control center settings - these may not be available in nix-darwin
    # controlcenter = {
    #   BatteryShowPercentage = true;
    #   Bluetooth = true;
    #   FocusModes = true;
    #   NowPlaying = true;
    #   Sound = true;
    #   WiFi = true;
    # };

    # Mission control settings
    spaces = {
      spans-displays = false;
    };

    # Login window settings
    loginwindow = {
      GuestEnabled = false;
      DisableConsoleAccess = false;
      SHOWFULLNAME = true;
      LoginwindowText = "Welcome to ${username}'s MacBook Pro";
    };

    # Software update settings - these may not be available in nix-darwin
    # SoftwareUpdate = {
    #   AutomaticallyInstallMacOSUpdates = false;
    #   AutomaticallyCheckForUpdates = true;
    #   CriticalUpdateInstall = true;
    #   ConfigDataInstall = true;
    # };

    # Energy saver settings - these may not be available in nix-darwin
    # energy = {
    #   ComputerSleep = 30;
    #   DisplaySleep = 10;
    #   DiskSleep = 0;
    #   WakeOnLAN = true;
    #   WakeOnNetworkAccess = true;
    #   ReduceBrightness = true;
    #   AutomaticRestartOnPowerLoss = true;
    # };

    # Time machine settings - these may not be available in nix-darwin
    # timemachine = {
    #   AutoBackup = false;
    #   MobileBackups = false;
    #   ShowInMenuBar = false;
    # };
  };

  # Host-specific security settings
  security.pam.services.sudo_local.touchIdAuth = true;
  security.pam.services.sudo_local.watchIdAuth = true;

  # Host-specific launchd services
  launchd.agents = {
    # User-specific services can be added here
    "user-env-sync" = {
      serviceConfig = {
        Label = "com.user.env.sync";
        ProgramArguments = [
          "${pkgs.bash}/bin/bash"
          "-c"
          ''
            # Sync environment variables
            echo "export PRIMARY_USER=${username}" > ~/.env
            echo "export HOSTNAME=darwin-${username}" >> ~/.env
            echo "export COMPUTER_NAME='${username}'s MacBook Pro'" >> ~/.env
          ''
        ];
        RunAtLoad = true;
      };
    };
  };

  # Import shared and Darwin-specific modules
  imports = [
    ../../modules/darwin
    ../../modules/shared
  ];
}
